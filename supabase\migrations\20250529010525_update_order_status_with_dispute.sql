-- Update the update_order_status function to support automatic dispute ticket creation
-- This migration must come after app_support schema creation
-- Drop the old function first to avoid overloading issues
DROP FUNCTION IF EXISTS app_provider.update_order_status (
  UUID,
  app_provider.ORDER_STATUS
)
;

-- anchor update_order_status
CREATE OR REPLACE FUNCTION app_provider.update_order_status (
  p_order_id UUID,
  p_new_status app_provider.ORDER_STATUS,
  p_dispute_description TEXT DEFAULT NULL
) RETURNS app_provider.order LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_order app_provider.order;
  v_updated_order app_provider.order;
  v_dispute_ticket app_support.ticket;
BEGIN
  -- Get the current order
  SELECT * INTO v_order
  FROM app_provider.order
  WHERE id = p_order_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Order not found.';
  END IF;

  -- Allow updates if the status is not changing
  IF p_new_status = v_order.order_status THEN
    RETURN v_order;
  END IF;

  -- Check if the current user is the sender
  IF v_current_user_id = v_order.sender_id THEN
    -- Check if user has permission to update orders as sender
    IF NOT app_access.has_capability('provider.order.submit') THEN
      RAISE EXCEPTION 'Insufficient permissions to update order status.';
    END IF;

    -- Check if the new status is allowed for the sender
    IF NOT (p_new_status = ANY(v_order.next_status_for_sender)) THEN
      RAISE EXCEPTION 'Invalid order status transition for sender from % to %.', v_order.order_status, p_new_status;
    END IF;

  -- Check if the current user is the receiver
  ELSIF v_current_user_id = v_order.receiver_id THEN
    -- Check if user has permission to update orders as receiver
    IF NOT app_access.has_capability('provider.order.view') THEN
      RAISE EXCEPTION 'Insufficient permissions to update order status.';
    END IF;

    -- Check if the new status is allowed for the receiver
    IF NOT (p_new_status = ANY(v_order.next_status_for_receiver)) THEN
      RAISE EXCEPTION 'Invalid order status transition for receiver from % to %.', v_order.order_status, p_new_status;
    END IF;

  -- Check if the user has admin permissions
  ELSIF app_access.has_capability('provider.order.all.control') THEN
    -- Admin can make any status transition, no restrictions
    NULL; -- Do nothing, allow the update

  ELSE
    RAISE EXCEPTION 'You do not have permission to update this order.';
  END IF;

  -- Update the order status
  UPDATE app_provider.order
  SET order_status = p_new_status
  WHERE id = p_order_id
  RETURNING * INTO v_updated_order;

  -- If the new status is 'in_dispute' and a dispute description is provided, create a dispute ticket
  IF p_new_status = 'in_dispute' AND p_dispute_description IS NOT NULL THEN
    SELECT * INTO v_dispute_ticket
    FROM app_support.create_ticket(
      p_title := 'Order Dispute - Order #' || SUBSTRING(p_order_id::TEXT, 1, 8),
      p_problem_description := p_dispute_description,
      p_type := 'dispute',
      p_disputed_order_id := p_order_id
    );
  END IF;

  RETURN v_updated_order;
END;
$$
;
