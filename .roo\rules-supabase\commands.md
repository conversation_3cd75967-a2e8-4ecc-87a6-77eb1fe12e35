---
type: "always_apply"
---

# Commands

## Running SQL Files

After migration file (.sql) changes, always run `pnpm supabase:reset-db-local-and-types`.

Do not run `supabase db reset --local`. Always prefer `pnpm supabase:reset-db-local-and-types`

## Running Tests

Do not run test commands in watch mode.

### All Tests

```bash
pnpm test supabase/__tests__
```

### Individual File

```bash
pnpm test supabase/__tests__/app_access.role.test.ts
```
